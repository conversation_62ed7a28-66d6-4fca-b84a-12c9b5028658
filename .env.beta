PORT=8080
NODE_ENV=beta
DB_HOST=localhost
DB_PORT=5432
DB_USER=your_database_user
DB_PASSWORD=your_database_password
DB_NAME=your_database_name
JWT_SECRET=supersecretkey
JWT_EXPIRATION=86400000
# Email SMTP Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=fqactehafmzlltzz
SMTP_FROM='"No Reply" <<EMAIL>>'
APP_URL=https://your-beta-domain
#Cors Configuration
CORS_ALLOWED_ORIGINS=https://your-beta-domain.com
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=86400