name: CI Workflow
on:
  push:
    branches: [ "main","auth*" ]
jobs:
  build:
    runs-on: [self-hosted, minikube]
    strategy:
      matrix:
        node-version: [20.x, 22.x]
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
      - name: Set up Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v3
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
      - name: Install dependencies
        run: npm ci
      - name: Run database migrations
        run: npx dotenv -e .env.development -- npx knex --knexfile src/database/knexfile.ts migrate:latest
      - name: Build project
        run: npm run build
      - name: Run tests
        run: npm test