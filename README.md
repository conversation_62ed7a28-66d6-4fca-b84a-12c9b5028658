# Backend Sample Authentication (NestJS + Knex + PostgreSQL)

[![Coverage Status](https://img.shields.io/badge/coverage-local--report-brightgreen)](./coverage/lcov-report/index.html)

> See [TESTING.md](./TESTING.md) for test, coverage, and badge instructions.

## Prerequisites
- Node.js (v18+ recommended)
- npm
- Docker & Docker Compose

## Environment Setup
1. Copy environment file:
   ```sh
   cp env.example .env
   ```

2. Configure your environment variables in `.env` file (see env.example for all required variables)

## Google OAuth Setup

### 1. Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select an existing one
3. Enable the Google+ API and Google OAuth2 API

### 2. Configure OAuth Consent Screen
1. Go to "APIs & Services" > "OAuth consent screen"
2. Choose "External" user type
3. Fill in the required information:
   - App name
   - User support email
   - Developer contact information
4. Add scopes: `email`, `profile`, `openid`
5. Add test users if needed

### 3. Create OAuth 2.0 Credentials
1. Go to "APIs & Services" > "Credentials"
2. Click "Create Credentials" > "OAuth 2.0 Client IDs"
3. Choose "Web application"
4. Configure authorized URIs:

**JavaScript Origins:**
```
http://localhost:8080
http://localhost:3000
```

**Redirect URIs:**
```
http://localhost:8080/oauth2/callback/google
http://localhost:3000/chidhagni/oauth2/redirect
myandroidapp://oauth2/redirect
myiosapp://oauth2/redirect
```

5. Copy the Client ID and Client Secret to your `.env` file

### 4. Environment Variables for Google OAuth
Add these to your `.env` file:
```env
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
GOOGLE_REDIRECT_URI=http://localhost:8080/oauth2/callback/google
GOOGLE_SCOPE=email,profile,openid
OAUTH2_AUTHORIZED_REDIRECT_URIS=http://localhost:3000/chidhagni/oauth2/redirect,myandroidapp://oauth2/redirect,myiosapp://oauth2/redirect
```

## CORS Configuration
The application includes configurable CORS settings. Add the following to your `.env` file:

```env
# CORS Configuration
CORS_ALLOWED_ORIGINS=http://localhost:8080,http://localhost:3000
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,PATCH,OPTIONS
CORS_ALLOW_CREDENTIALS=true
CORS_MAX_AGE=3600
```

**Default CORS Settings:**
- **Allowed Origins**: `http://localhost:8080`, `http://localhost:3000`
- **Allowed Methods**: `GET`, `POST`, `PUT`, `DELETE`, `PATCH`, `OPTIONS`
- **Allow Credentials**: `true`
- **Max Age**: `3600` seconds

You can customize these settings by modifying the environment variables in your `.env` file.

## Database (PostgreSQL via Docker)
1. Ensure Docker is running.
2. Start PostgreSQL:
   ```sh
   docker-compose up -d
   ```
3. (First time only) Create the external network if needed:
   ```sh
   docker network create auth-network
   ```

## Install Dependencies
```sh
npm install
```


## Running the Docker
Start the Docker container:
```sh
docker-compose up -d
```

## Database Migration
Run all migrations to set up the schema:
```sh
npx dotenv -e .env.development -- npx knex --knexfile src/database/knexfile.ts migrate:latest
```


## Running the App
Start the NestJS app in development mode:
```sh
npm start
```

You should see logs for:
- Database connection established
- JWT initialized
- NestJS app running on port 8080

## Google OAuth Flow

### Frontend Integration
The backend supports the following OAuth flow:

1. **Initiate OAuth**: `GET /auth/google`
   - Redirects user to Google consent screen

2. **OAuth Callback**: `GET /oauth2/callback/google`
   - Handles Google's authorization code
   - Exchanges code for tokens
   - Creates/updates user in database
   - Redirects to frontend with tokens

3. **Token Response Format**:
   ```json
   {
     "accessToken": "jwt-token",
     "refreshToken": "refresh-token",
     "expiresAt": "2024-01-01T00:00:00.000Z",
     "refreshExpiresAt": "2024-01-30T00:00:00.000Z",
     "user": {
       "id": "user-id",
       "email": "<EMAIL>",
       "firstName": "John",
       "lastName": "Doe",
       "picture": "https://...",
       "isEmailVerified": true
     }
   }
   ```

### Frontend Redirect Handler
After successful OAuth, users are redirected to:
```
http://localhost:3000/chidhagni/oauth2/redirect?accessToken=...&refreshToken=...&expiresAt=...&refreshExpiresAt=...&user=...
```

## Testing
### Unit Tests
```sh
npm run test
```
### E2E Tests
```sh
npm run test:e2e
```

## Project Structure
```
src/
├── auth/
│   ├── dto/
│   ├── auth.controller.ts
│   ├── auth.service.ts
│   ├── auth.module.ts
│   ├── auth.guard.ts
│   ├── jwt.strategy.ts
│   ├── google.strategy.ts
├── users/
│   ├── users.service.ts
│   ├── users.module.ts
├── config/
│   ├── database.config.ts
│   ├── google-oauth.config.ts
├── database/
│   ├── knexfile.ts
│   ├── migrations/
├── app.module.ts
├── main.ts
.env
```

## API Endpoints

### Authentication Endpoints
- `POST /api/v1/register` - Register new user
- `POST /api/v1/login` - User login
- `POST /api/v1/logout` - User logout
- `GET /api/v1/verify-email` - Verify email with token
- `POST /api/v1/forgot-password` - Request password reset
- `POST /api/v1/reset-password` - Reset password with token

### OAuth2 Endpoints
- `GET /auth/google` - Initiate Google OAuth login
- `GET /oauth2/callback/google` - Google OAuth callback

## Notes
- Mock email sending is logged to the console for verification and password reset flows.
- All endpoints validate input using class-validator.
- JWT and session management are implemented using best practices.
- Google OAuth automatically creates users on first login.
- Users are uniquely identified by their Google email address.

## API Documentation

Swagger UI is available at: [http://localhost:8080/api-docs](http://localhost:8080/api-docs) 

## Dockerizing for Multiple Environments

This project supports three environments: development (local), beta (staging), and production. The Dockerfile does not copy any .env file; instead, you provide the correct .env file at runtime.

### Run for Each Environment (Dynamic Selection)

#### Development (Local)
```sh
# Run with development environment
docker run -v $(pwd)/.env.development:/app/.env -e NODE_ENV=development -p 8080:8080 myapp:dev
```

#### Beta (Staging)
```sh
# Run with beta (staging) environment
docker run -v $(pwd)/.env.beta:/app/.env -e NODE_ENV=beta -p 8080:8080 myapp:beta
```

#### Production
```sh
# Run with production environment
docker run -v $(pwd)/.env.production:/app/.env -e NODE_ENV=production -p 8080:8080 myapp:prod
```

### Using Docker Compose
You can specify the env file for each service in your docker-compose.yml:

```yaml
services:
  app-dev:
    build: .
    volumes:
      - ./env.development:/app/.env
    environment:
      - NODE_ENV=development
    ports:
      - "8080:8080"

  app-beta:
    build: .
    volumes:
      - ./env.beta:/app/.env
    environment:
      - NODE_ENV=beta
    ports:
      - "8081:8080"

  app-prod:
    build: .
    volumes:
      - ./env.production:/app/.env
    environment:
      - NODE_ENV=production
    ports:
      - "8082:8080"
```

This approach allows you to dynamically select the environment at runtime without rebuilding the image. 