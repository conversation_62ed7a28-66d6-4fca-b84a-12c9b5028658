version: '3.3'
services:
  postgres:
    image: postgres:13
    container_name: userauth-postgres
    ports:
      - '5433:5432'
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
      POSTGRES_DB: userauth
    volumes:
      - userauth_pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: on-failure
    networks:
      - auth-network
    security_opt:
      - no-new-privileges:true
  sonarqube:
    image: sonarqube:community
    container_name: sonarqube
    ports:
      - "9002:9000"
    environment:
      - SONAR_ES_BOOTSTRAP_CHECKS_DISABLE=true
    volumes:
      - sonarqube_data:/opt/sonarqube/data
      - sonarqube_logs:/opt/sonarqube/logs
      - sonarqube_extensions:/opt/sonarqube/extensions
    security_opt:
      - no-new-privileges:true
    read_only: true
  app:
    build: .
    container_name: userauth-app
    env_file:
      - .env.development  # Change to .env.beta or .env.production as needed
    ports:
      - "3000:3000"
    depends_on:
      - postgres
    networks:
      - auth-network
    restart: on-failure
    security_opt:
      - no-new-privileges:true
    # To use a different environment, override the env_file value above or use:
    # docker-compose --env-file .env.beta up --build
volumes:
  userauth_pgdata:
    driver: local
  sonarqube_data:
  sonarqube_logs:
  sonarqube_extensions:
networks:
  auth-network:
    driver: bridge