import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe, HttpStatus, ExecutionContext } from '@nestjs/common';
import request, { Response } from 'supertest';
import { AppModule } from '../../src/app.module';
import { Auth<PERSON><PERSON>roller, OAuth2Controller } from '../../src/auth/auth.controller';
import { AuthService } from '../../src/auth/auth.service';
import { ConfigService } from '@nestjs/config';
import { AuthGuard } from '@nestjs/passport';
import { GoogleStrategy } from '../../src/auth/google.strategy';
import { JwtStrategy } from '../../src/auth/jwt.strategy';
import { startTestDb, stopTestDb, db } from './test-db-setup';

jest.setTimeout(60000);

describe('AuthController (e2e)', () => {
  let app: INestApplication;

  beforeAll(async () => {
    await startTestDb();

    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    app.setGlobalPrefix('api/v1');
    await app.init();
  });

  afterAll(async () => {
    await stopTestDb();
  });

  beforeEach(async () => {
    // Clean up users table before each test
    await db.raw('TRUNCATE TABLE users RESTART IDENTITY CASCADE;');
  });

  it('should register a user, verify email, create user, and persist in the database', async () => {
    const userDto = {
      email: '<EMAIL>',
      name: 'Test User',
      mobileNumber: '**********',
    };
    // Step 1: Register
    await request(app.getHttpServer())
      .post('/api/v1/register')
      .send(userDto)
      .expect(201);
    // Step 2: Fetch verification token from DB
    const verification = await db('user_verification').where({ contact_value: userDto.email }).first();
    expect(verification).toBeDefined();
    // Step 3: Verify email
    await request(app.getHttpServer())
      .get(`/api/v1/verify-email?token=${verification.verification_token}`)
      .expect(200);
    // Step 4: Create user
    const createUserDto = {
      email: userDto.email,
      password: 'password123',
      ipAddress: '*********',
      deviceDetails: 'Chrome 120.0.0.0 on Windows 10',
    };
    await request(app.getHttpServer())
      .post('/api/v1/create-user')
      .send(createUserDto)
      .expect(201);
    // Step 5: Check DB
    const users = await db('users').where({ email: userDto.email });
    expect(users).toHaveLength(1);
    expect(users[0].email).toBe(userDto.email);
  });

  it('should login a registered user', async () => {
    const userDto = {
      email: '<EMAIL>',
      name: 'Test User2',
      mobileNumber: '1234567891',
    };
    // Register
    await request(app.getHttpServer())
      .post('/api/v1/register')
      .send(userDto)
      .expect(201);
    // Verify
    const verification = await db('user_verification').where({ contact_value: userDto.email }).first();
    await request(app.getHttpServer())
      .get(`/api/v1/verify-email?token=${verification.verification_token}`)
      .expect(200);
    // Create user
    const createUserDto = {
      email: userDto.email,
      password: 'password123',
      ipAddress: '*********',
      deviceDetails: 'Firefox 120.0.0.0 on Windows 10',
    };
    await request(app.getHttpServer())
      .post('/api/v1/create-user')
      .send(createUserDto)
      .expect(201);
    // Now, login
    const loginDto = {
      email: userDto.email,
      password: 'password123',
      ipAddress: '*********',
      deviceDetails: 'Firefox 120.0.0.0 on Windows 10',
      overrideExistingLogins: false,
    };
    const res = await request(app.getHttpServer())
      .post('/api/v1/login')
      .send(loginDto)
      .expect(201);
    expect(res.body).toHaveProperty('sessionToken');
    expect(res.body).toHaveProperty('refreshToken');
  });

  it('should request a password reset for a registered user', async () => {
    const userDto = {
      email: '<EMAIL>',
      name: 'Reset User',
      mobileNumber: '1234567892',
    };
    // Register, verify, create user
    await request(app.getHttpServer()).post('/api/v1/register').send(userDto).expect(201);
    const verification = await db('user_verification').where({ contact_value: userDto.email }).first();
    await request(app.getHttpServer()).get(`/api/v1/verify-email?token=${verification.verification_token}`).expect(200);
    const createUserDto = {
      email: userDto.email,
      password: 'password123',
      ipAddress: '*********',
      deviceDetails: 'Edge 120.0.0.0 on Windows 10',
    };
    await request(app.getHttpServer()).post('/api/v1/create-user').send(createUserDto).expect(201);
    // Request password reset
    const forgotDto = {
      email: userDto.email,
      ipAddress: '*********',
      deviceDetails: 'Edge 120.0.0.0 on Windows 10',
    };
    const res = await request(app.getHttpServer())
      .post('/api/v1/forgot-password')
      .send(forgotDto)
      .expect(201);
    expect(res.body).toHaveProperty('message');
  });

  it('should reset password for a user with a valid reset token', async () => {
    const userDto = {
      email: '<EMAIL>',
      name: 'Reset2 User',
      mobileNumber: '1234567893',
    };
    // Register, verify, create user
    await request(app.getHttpServer()).post('/api/v1/register').send(userDto).expect(201);
    const verification = await db('user_verification').where({ contact_value: userDto.email }).first();
    await request(app.getHttpServer()).get(`/api/v1/verify-email?token=${verification.verification_token}`).expect(200);
    const createUserDto = {
      email: userDto.email,
      password: 'password123',
      ipAddress: '*********',
      deviceDetails: 'Safari 120.0.0.0 on macOS',
    };
    await request(app.getHttpServer()).post('/api/v1/create-user').send(createUserDto).expect(201);
    // Request password reset
    const forgotDto = {
      email: userDto.email,
      ipAddress: '*********',
      deviceDetails: 'Safari 120.0.0.0 on macOS',
    };
    await request(app.getHttpServer()).post('/api/v1/forgot-password').send(forgotDto).expect(201);
    // Get reset token from DB
    const resetRecord = await db('user_password_reset').where({}).orderBy('created_at', 'desc').first();
    // Reset password
    const resetDto = {
      resetToken: resetRecord.reset_token,
      newPassword: 'newpassword123',
      ipAddress: '*********',
      deviceDetails: 'Safari 120.0.0.0 on macOS',
    };
    const res = await request(app.getHttpServer())
      .post('/api/v1/reset-password')
      .send(resetDto)
      .expect(201);
    expect(res.body).toHaveProperty('message');
  });

  it('should resend verification link for a registered user', async () => {
    const userDto = {
      email: '<EMAIL>',
      name: 'Resend User',
      mobileNumber: '1234567894',
    };
    // Register
    await request(app.getHttpServer()).post('/api/v1/register').send(userDto).expect(201);
    // Resend verification link
    const resendDto = { email: userDto.email };
    const res = await request(app.getHttpServer())
      .post('/api/v1/resend-verification-link')
      .send(resendDto)
      .expect(201);
    expect(res.body).toHaveProperty('message');
  });

  describe('OAuth2Controller', () => {
    it('/api/v1/auth/google (GET) should redirect to Google', async () => {
      await request(app.getHttpServer())
        .get('/api/v1/auth/google')
        .expect(302);
    });
      
    it('/api/v1/oauth2/status (GET)', () => {
        return request(app.getHttpServer())
            .get('/api/v1/oauth2/status')
            .expect(200)
            .then((res: Response) => {
                expect(res.body.success).toBe(true);
                expect(res.body.googleOAuth).toBeDefined();
            });
    });
  });
});
