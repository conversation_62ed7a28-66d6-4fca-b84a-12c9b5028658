import { UsersService } from '../../src/users/users.service';
import { setupTestDb, teardownTestDb, cleanDb, TestDbContext } from './test-utils';
import { v4 as uuidv4 } from 'uuid';

jest.setTimeout(60000);

describe('UsersService (Integration)', () => {
  let dbCtx: TestDbContext;
  let usersService: UsersService;

  beforeAll(async () => {
    dbCtx = await setupTestDb();
  });

  afterAll(async () => {
    await teardownTestDb(dbCtx);
  });

  beforeEach(async () => {
    await cleanDb(dbCtx.knex);
    usersService = new UsersService(dbCtx.knex);
  });

  it('should create a user and find by email', async () => {
    const userData = {
      id: uuidv4(),
      email: '<EMAIL>',
      name: 'Test User',
      first_name: 'Test',
      last_name: 'User',
      password: 'hashedpassword',
      email_verified: false,
      mobile_number: '1234567890',
    };
    const created = await usersService.createUser(userData);
    expect(created.email).toBe(userData.email);
    expect(created.first_name).toBe(userData.first_name);
    expect(created.last_name).toBe(userData.last_name);
    expect(created.email_verified).toBe(false);

    const found = await usersService.findByEmail(userData.email);
    expect(found).toBeDefined();
    expect(found.email).toBe(userData.email);
  });

  it('should return undefined for non-existent email', async () => {
    const found = await usersService.findByEmail('<EMAIL>');
    expect(found).toBeUndefined();
  });
}); 